# Installation Guide for TFT Well Project

## 🚨 Error: "No matching distribution found for requirements.txt"

This error typically means one of the following:

1. **Wrong pip command syntax**
2. **Package version conflicts**
3. **Python version incompatibility**
4. **Network/proxy issues**

## ✅ Step-by-Step Installation

### Step 1: Activate Your Environment

```bash
# Windows
C:\Users\<USER>\codellm\Scripts\activate

# Verify you're in the right environment
where python
# Should show: C:\Users\<USER>\codellm\Scripts\python.exe
```

### Step 2: Update pip

```bash
python -m pip install --upgrade pip
```

### Step 3: Install Dependencies (Choose One Method)

#### Method A: Install from requirements.txt
```bash
pip install -r requirements.txt
```

#### Method B: Install minimal requirements (if Method A fails)
```bash
pip install -r requirements-minimal.txt
```

#### Method C: Install packages individually (most reliable)
```bash
pip install "numpy>=1.17.4"
pip install "pandas>=0.25.3"
pip install "scikit-learn>=0.22"
pip install "tensorflow>=2.8.0"
pip install "tensorflow-probability>=0.8.0"
```

#### Method D: Install optional packages (for data processing)
```bash
pip install "wget>=3.2"
pip install "pyunpack>=0.1.2"
pip install "patool>=1.12"
```

### Step 4: Verify Installation

```bash
python -c "import tensorflow as tf; print('TensorFlow version:', tf.__version__)"
python -c "import numpy as np; print('NumPy version:', np.__version__)"
python -c "import pandas as pd; print('Pandas version:', pd.__version__)"
```

### Step 5: Run Setup Check

```bash
python setup_environment.py
```

## 🔧 Troubleshooting Common Issues

### Issue 1: "No matching distribution found"
**Solution**: Try installing packages individually with exact versions:
```bash
pip install "tensorflow>=2.8.0"
pip install "numpy>=1.17.4"
pip install "pandas>=0.25.3"
pip install "scikit-learn>=0.22"
```

### Issue 2: "Could not find a version that satisfies the requirement"
**Solution**: Check your Python version:
```bash
python --version
# TensorFlow 2.10+ requires Python 3.7-3.11
```

### Issue 3: Network/Proxy Issues
**Solution**: Try with different index:
```bash
pip install --index-url https://pypi.org/simple/ tensorflow
```

### Issue 4: Permission Errors
**Solution**: Install in user space:
```bash
pip install --user tensorflow
```

## 🎯 Quick Test

After installation, test if everything works:

```bash
python -c "
import tensorflow.compat.v1 as tf
tf.compat.v1.disable_v2_behavior()
print('✅ TensorFlow import successful!')
print('TensorFlow version:', tf.__version__)
"
```

## 📞 Still Having Issues?

1. **Check Python version**: `python --version` (should be 3.7-3.11)
2. **Check pip version**: `pip --version`
3. **Clear pip cache**: `pip cache purge`
4. **Try conda instead**: `conda install tensorflow`

## 🚀 Alternative: Use Conda

If pip continues to fail, try conda:

```bash
conda create -n tft_env python=3.10
conda activate tft_env
conda install tensorflow numpy pandas scikit-learn matplotlib
```

## ✅ Success Indicators

You'll know everything is working when:
- ✅ All imports work without errors
- ✅ `python setup_environment.py` runs successfully
- ✅ No import warnings in VS Code (after restart)
