{"python.defaultInterpreterPath": "C:\\Users\\<USER>\\codellm\\Scripts\\python.exe", "python.analysis.extraPaths": ["./libs", "./data_formatters", "./expt_settings"], "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic", "python.analysis.diagnosticSeverityOverrides": {"reportMissingImports": "warning", "reportMissingModuleSource": "none"}, "pylance.insidersChannel": "off", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.analysis.diagnosticMode": "workspace"}