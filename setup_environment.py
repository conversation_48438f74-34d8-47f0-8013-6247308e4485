#!/usr/bin/env python3
"""
Environment setup script for TFT Well project.
This script helps verify and install the required dependencies.
"""

import subprocess
import sys
import importlib.util

def check_package(package_name, import_name=None):
    """Check if a package is installed and importable."""
    if import_name is None:
        import_name = package_name
    
    try:
        spec = importlib.util.find_spec(import_name)
        if spec is not None:
            print(f"✅ {package_name} is installed")
            return True
        else:
            print(f"❌ {package_name} is not installed")
            return False
    except ImportError:
        print(f"❌ {package_name} is not installed")
        return False

def install_package(package_name):
    """Install a package using pip."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ Successfully installed {package_name}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ Failed to install {package_name}")
        return False

def main():
    """Main setup function."""
    print("🔧 TFT Well Environment Setup")
    print("=" * 40)
    
    # Check Python version
    python_version = sys.version_info
    print(f"Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("⚠️  Warning: Python 3.8+ is recommended")
    
    print("\n📦 Checking dependencies...")
    
    # Core dependencies to check
    dependencies = [
        ("tensorflow", "tensorflow"),
        ("numpy", "numpy"),
        ("pandas", "pandas"),
        ("scikit-learn", "sklearn"),
        ("matplotlib", "matplotlib"),
    ]
    
    missing_packages = []
    
    for package_name, import_name in dependencies:
        if not check_package(package_name, import_name):
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\n🔧 Installing missing packages: {', '.join(missing_packages)}")

        # Try multiple installation methods
        success = False

        # Method 1: Install from requirements.txt
        try:
            print("Trying: pip install -r requirements.txt")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("✅ Successfully installed all requirements")
            success = True
        except subprocess.CalledProcessError:
            print("❌ Failed to install from requirements.txt")

        # Method 2: Install minimal requirements
        if not success:
            try:
                print("Trying: pip install -r requirements-minimal.txt")
                subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements-minimal.txt"])
                print("✅ Successfully installed minimal requirements")
                success = True
            except subprocess.CalledProcessError:
                print("❌ Failed to install from requirements-minimal.txt")

        # Method 3: Install packages individually
        if not success:
            print("Trying to install packages individually...")
            essential_packages = ["tensorflow", "numpy", "pandas", "scikit-learn"]
            for package in essential_packages:
                try:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                    print(f"✅ Installed {package}")
                except subprocess.CalledProcessError:
                    print(f"❌ Failed to install {package}")

        if not success:
            print("\n⚠️  Automatic installation failed. Please try manual installation:")
            print("1. pip install tensorflow")
            print("2. pip install numpy pandas scikit-learn")
            print("3. See INSTALLATION_GUIDE.md for detailed instructions")
    
    print("\n🧪 Testing TensorFlow import...")
    try:
        import tensorflow as tf  # type: ignore[import]
        print(f"✅ TensorFlow {tf.__version__} imported successfully")

        # Test TensorFlow compatibility mode
        import tensorflow.compat.v1 as tf_v1  # type: ignore[import]
        print("✅ TensorFlow v1 compatibility mode available")

        # Test basic TensorFlow operation
        _ = tf_v1.constant([1, 2, 3])
        print("✅ TensorFlow operations working")

    except ImportError as e:
        print(f"❌ TensorFlow import failed: {e}")
        print("Please install TensorFlow: pip install tensorflow")
        print("See INSTALLATION_GUIDE.md for detailed instructions")
    
    print("\n🎉 Setup complete!")
    print("\nNext steps:")
    print("1. Restart VS Code")
    print("2. Select the correct Python interpreter (Ctrl+Shift+P -> Python: Select Interpreter)")
    print("3. Choose: C:\\Users\\<USER>\\codellm\\Scripts\\python.exe")

if __name__ == "__main__":
    main()
